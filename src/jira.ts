import fetch from 'node-fetch';

// Jira configuration
const JIRA_SERVER_URL = 'https://jira.skywindgroup.com';
const JIRA_CREDENTIALS = 'agentai:UY654eswq@'; // From load.sh

export interface JiraCommentRequest {
  body: string;
}

export interface JiraCommentResponse {
  id: string;
  body: string;
  created: string;
}

export function validateJiraIssueId(issueId: string): boolean {
  const pattern = /^[A-Z]+-[0-9]+$/;
  return pattern.test(issueId);
}

export async function postJiraComment(issueId: string, comment: string): Promise<void> {
  const url = `${JIRA_SERVER_URL}/rest/api/2/issue/${issueId}/comment`;
  
  const requestBody: JiraCommentRequest = {
    body: comment,
  };

  try {
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Authorization': `Basic ${Buffer.from(JIRA_CREDENTIALS).toString('base64')}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      body: JSON.stringify(requestBody),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`HTTP ${response.status}: ${errorText}`);
    }

    const result = await response.json() as JiraCommentResponse;
    console.log('✓ Comment posted successfully to JIRA issue:', issueId);
    console.log('Comment ID:', result.id);
  } catch (error) {
    console.error('✗ Failed to post comment to JIRA:', error);
    throw error;
  }
}
