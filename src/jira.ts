import fetch from 'node-fetch';

export interface JiraCommentRequest {
  body: string;
}

export interface JiraCommentResponse {
  id: string;
  body: string;
  created: string;
}

export interface JiraConfig {
  serverUrl: string;
  credentials: string;
}

export class JiraClient {
  private config: JiraConfig;

  constructor(config?: Partial<JiraConfig>) {
    const serverUrl = config?.serverUrl || process.env.JIRA_SERVER_URL;
    const credentials = config?.credentials || process.env.JIRA_CREDENTIALS;

    if (!serverUrl) {
      throw new Error('JIRA_SERVER_URL is required. Set it via environment variable or pass it in config.');
    }

    if (!credentials) {
      throw new Error('JIRA_CREDENTIALS is required. Set it via environment variable or pass it in config.');
    }

    this.config = {
      serverUrl,
      credentials,
    };
  }

  validateJiraIssueId(issueId: string): boolean {
    const pattern = /^[A-Z]+-[0-9]+$/;
    return pattern.test(issueId);
  }

  async postComment(issueId: string, comment: string): Promise<void> {
    const url = `${this.config.serverUrl}/rest/api/2/issue/${issueId}/comment`;

    const requestBody: JiraCommentRequest = {
      body: comment,
    };

    try {
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Authorization': `Basic ${Buffer.from(this.config.credentials).toString('base64')}`,
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

      const result = await response.json() as JiraCommentResponse;
      console.log('✓ Comment posted successfully to JIRA issue:', issueId);
      console.log('Comment ID:', result.id);
    } catch (error) {
      console.error('✗ Failed to post comment to JIRA:', error);
      throw error;
    }
  }
}

// Legacy functions for backward compatibility
export function validateJiraIssueId(issueId: string): boolean {
  const client = new JiraClient();
  return client.validateJiraIssueId(issueId);
}

export async function postJiraComment(issueId: string, comment: string): Promise<void> {
  const client = new JiraClient();
  await client.postComment(issueId, comment);
}
