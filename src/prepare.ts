import { parseArgs } from 'node:util';
import fetch from 'node-fetch';

// Jira configuration
const JIRA_SERVER_URL = 'https://jira.skywindgroup.com';
const JIRA_CREDENTIALS = 'agentai:UY654eswq@'; // From load.sh

interface JiraCommentRequest {
  body: string;
}

interface JiraCommentResponse {
  id: string;
  body: string;
  created: string;
}

interface CliArgs {
  issueId?: string;
  help?: boolean;
}

function parseCliArgs(): CliArgs {
  try {
    const { values } = parseArgs({
      args: process.argv.slice(2),
      options: {
        'issue-id': {
          type: 'string',
          short: 'i',
        },
        help: {
          type: 'boolean',
          short: 'h',
        },
      },
    });

    return {
      issueId: values['issue-id'] as string,
      help: values.help as boolean,
    };
  } catch (error) {
    console.error('Error parsing arguments:', error);
    process.exit(1);
  }
}

function showHelp(): void {
  console.log(`
Usage: tsx src/prepare.ts --issue-id <JIRA_ISSUE_ID>

Options:
  -i, --issue-id <ID>    JIRA issue ID (e.g., SWS-34515)
  -h, --help            Show this help message

Examples:
  tsx src/prepare.ts --issue-id SWS-34515
  tsx src/prepare.ts -i SWS-34515
`);
}

function validateJiraIssueId(issueId: string): boolean {
  const pattern = /^[A-Z]+-[0-9]+$/;
  return pattern.test(issueId);
}

async function postJiraComment(issueId: string, comment: string): Promise<void> {
  const url = `${JIRA_SERVER_URL}/rest/api/2/issue/${issueId}/comment`;

  const requestBody: JiraCommentRequest = {
    body: comment,
  };

  try {
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Authorization': `Basic ${Buffer.from(JIRA_CREDENTIALS).toString('base64')}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      body: JSON.stringify(requestBody),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`HTTP ${response.status}: ${errorText}`);
    }

    const result = await response.json() as JiraCommentResponse;
    console.log('✓ Comment posted successfully to JIRA issue:', issueId);
    console.log('Comment ID:', result.id);
  } catch (error) {
    console.error('✗ Failed to post comment to JIRA:', error);
    throw error;
  }
}

async function run(): Promise<void> {
  const args = parseCliArgs();

  if (args.help) {
    showHelp();
    return;
  }

  if (!args.issueId) {
    console.error('Error: JIRA issue ID is required');
    showHelp();
    process.exit(1);
  }

  if (!validateJiraIssueId(args.issueId)) {
    console.error(`Error: Invalid JIRA issue ID format: '${args.issueId}'`);
    console.error('Expected format: PROJECT-NUMBER (e.g., SWS-34515)');
    process.exit(1);
  }

  console.log(`Processing JIRA issue: ${args.issueId}`);

  try {
    // Step 1: Create initial tracking comment
    await postJiraComment(args.issueId, 'AI is working…');
    console.log('✓ Initial tracking comment posted successfully');
  } catch (error) {
    console.error('✗ Failed to process JIRA issue:', error);
    process.exit(1);
  }
}

run().catch((error) => {
  console.error('Unhandled error:', error);
  process.exit(1);
});
